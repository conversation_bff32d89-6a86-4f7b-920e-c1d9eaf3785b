
import 'package:gp_h5/flavors.dart';

/// Defines the flavor of the app
enum Flavor {
  gp, // 测试环境
  yhxt, // 沅和信投 (生产-
  bszb, // 宝石资本 (生产-租户站)
  dyzb, // 德盈资本 (生产-租户站)
  rsyp, // 荣顺优配 (生产-演示站)
}

/// Configuration for the app based on flavor

// app_config.dart

class AppConfig {
  final String appName;
  final String siteId;
  final String appId;


  static AppConfig? _instance;

  const AppConfig({
    required this.appName,
    required this.siteId,
    required this.appId,
  });

  /// Initializes the singleton instance with the provided configuration.
  static void initialize(AppConfig config) {
    _instance = config;
  }

  /// Provides access to the singleton instance.
  ///
  /// Throws an assertion error if accessed before initialization.
  static AppConfig get instance {
    assert(_instance != null, 'AppConfig must be initialized before accessing instance');
    return _instance!;
  }
  /// Returns a list of OSS URLs for the app configuration based on site ID and environment
  static List<String> getOssList({String? siteId}) {
    final regions = [
      {'prefix': 'rs-1337543130', 'region': 'ap-shanghai'},
      {'prefix': 'bj-1337543130', 'region': 'ap-beijing'}, 
      {'prefix': 'gz-1337543130', 'region': 'ap-guangzhou'},
      {'prefix': 'cq-1337543130', 'region': 'ap-chongqing'},
      {'prefix': 'xg-1337543130', 'region': 'ap-hongkong'},
    ];

    return regions.map((region) {
      final prefix = region['prefix'];
      final regionName = region['region'];
      return 'https://$prefix.cos.$regionName.myqcloud.com/${F.channel}/${siteId ?? instance.siteId}/app_api.json';
    }).toList();
  }
}
