import 'package:flutter_test/flutter_test.dart';
import 'package:gp_h5/config/flavors/app_config.dart';
import 'package:gp_h5/flavors.dart';

void main() {
  group('AppConfig Tests', () {
    setUp(() {
      // Initialize AppConfig for testing
      AppConfig.initialize(AppConfig(
        appName: 'Test App',
        siteId: '1',
        appId: 'com.test.app',
      ));
    });

    test('getOssList returns correct URLs', () {
      final ossList = AppConfig.getOssList();
      
      expect(ossList, isNotEmpty);
      expect(ossList.length, equals(5));
      
      // Check that all URLs are properly formatted
      for (final url in ossList) {
        expect(url, startsWith('https://'));
        expect(url, contains('.cos.'));
        expect(url, contains('.myqcloud.com'));
        expect(url, endsWith('/app_api.json'));
      }
      
      // Check the first URL (which will be shown in WebView)
      final firstUrl = ossList.first;
      expect(firstUrl, contains('rs-1337543130.cos.ap-shanghai.myqcloud.com'));
      expect(firstUrl, contains('/${F.channel}/1/app_api.json'));
    });

    test('getOssList with custom siteId', () {
      final ossList = AppConfig.getOssList(siteId: '999');
      
      expect(ossList, isNotEmpty);
      
      // Check that custom siteId is used
      for (final url in ossList) {
        expect(url, contains('/999/app_api.json'));
      }
    });

    test('AppConfig instance is properly initialized', () {
      final instance = AppConfig.instance;
      
      expect(instance.appName, equals('Test App'));
      expect(instance.siteId, equals('1'));
      expect(instance.appId, equals('com.test.app'));
    });
  });
}
